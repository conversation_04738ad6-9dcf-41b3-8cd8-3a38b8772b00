/* Mapbox GL CSS */
@import 'mapbox-gl/dist/mapbox-gl.css';

@import "tailwindcss";

/* AeroFarm Farming Color Palette */
:root {
  /* Primary Farming Colors */
  --farm-green-dark: #2D5016;    /* Rich farmland */
  --farm-green: #22C55E;         /* Growing crops */
  --farm-green-light: #86EFAC;   /* Fresh growth */

  /* Harvest & Grain Colors */
  --farm-gold: #F4A261;          /* Golden harvest */
  --farm-yellow: #FCD34D;        /* Grain/corn */
  --farm-amber: #F59E0B;         /* Wheat */

  /* Earth & Soil Colors */
  --farm-brown: #8B4513;         /* Rich soil */
  --farm-brown-light: #D2B48C;   /* Sandy soil */

  /* Water & Sky Colors */
  --farm-blue: #219EBC;          /* Irrigation water */
  --farm-blue-light: #7DD3FC;    /* Clear sky */
  --farm-blue-dark: #0F172A;     /* Deep water */

  /* Status Colors */
  --farm-success: #22C55E;       /* Healthy crops */
  --farm-warning: #F59E0B;       /* Attention needed */
  --farm-error: #EF4444;         /* Problem areas */

  /* Neutral Colors */
  --farm-gray-50: #F9FAFB;
  --farm-gray-100: #F3F4F6;
  --farm-gray-200: #E5E7EB;
  --farm-gray-600: #4B5563;
  --farm-gray-800: #1F2937;
  --farm-gray-900: #111827;
}

/* Custom styles for AeroFarm */
body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, var(--farm-green-light) 0%, var(--farm-blue-light) 100%);
  min-height: 100vh;
}

/* Ensure map container takes full height */
.mapboxgl-map {
  font-family: inherit;
}

/* Farming-themed button styles */
.farm-button {
  @apply px-6 py-3 rounded-xl font-semibold transition-all duration-300 shadow-lg;
  @apply transform hover:scale-105 active:scale-95;
  min-height: 44px; /* Touch-friendly minimum */
}

.farm-button-primary {
  background: linear-gradient(135deg, var(--farm-green) 0%, var(--farm-green-dark) 100%);
  @apply text-white shadow-green-500/25;
}

.farm-button-primary:hover {
  background: linear-gradient(135deg, var(--farm-green-dark) 0%, #1a3d0f 100%);
  @apply shadow-green-500/40;
}

.farm-button-secondary {
  background: linear-gradient(135deg, var(--farm-gold) 0%, var(--farm-amber) 100%);
  @apply text-white shadow-yellow-500/25;
}

.farm-button-secondary:hover {
  background: linear-gradient(135deg, var(--farm-amber) 0%, #d97706 100%);
  @apply shadow-yellow-500/40;
}

.farm-button-water {
  background: linear-gradient(135deg, var(--farm-blue) 0%, var(--farm-blue-dark) 100%);
  @apply text-white shadow-blue-500/25;
}

.farm-button-water:hover {
  background: linear-gradient(135deg, var(--farm-blue-dark) 0%, #0c1426 100%);
  @apply shadow-blue-500/40;
}

/* Floating Action Button */
.floating-action-button {
  @apply fixed bottom-6 right-6 w-16 h-16 rounded-full shadow-2xl;
  @apply flex items-center justify-center text-white text-2xl;
  @apply transform transition-all duration-300 hover:scale-110 active:scale-95;
  background: linear-gradient(135deg, var(--farm-green) 0%, var(--farm-green-dark) 100%);
  z-index: 1000;
  box-shadow: 0 8px 32px rgba(34, 197, 94, 0.4);
}

.floating-action-button:hover {
  box-shadow: 0 12px 40px rgba(34, 197, 94, 0.6);
}

/* Floating Panel Styles */
.floating-panel {
  @apply bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl border border-white/20;
  @apply p-4 m-4;
}

.floating-header {
  @apply bg-white/90 backdrop-blur-md shadow-lg border-b border-green-100;
  @apply px-6 py-3;
}

/* Farm Status Indicators */
.status-indicator {
  @apply flex items-center space-x-2 px-3 py-2 rounded-full text-sm font-medium;
}

.status-indicator.gps-connected {
  @apply bg-green-100 text-green-800;
}

.status-indicator.weather {
  @apply bg-blue-100 text-blue-800;
}

.status-indicator.zones {
  @apply bg-yellow-100 text-yellow-800;
}

/* Leaflet map styles with farming theme */
.leaflet-container {
  height: 100% !important;
  width: 100% !important;
  min-height: 400px !important;
  border-radius: 0 !important;
}

/* Custom map controls styling */
.leaflet-control-zoom {
  border: none !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
}

.leaflet-control-zoom a {
  background: var(--farm-green) !important;
  color: white !important;
  border: none !important;
  border-radius: 8px !important;
  font-size: 18px !important;
  font-weight: bold !important;
  width: 40px !important;
  height: 40px !important;
  line-height: 40px !important;
}

.leaflet-control-zoom a:hover {
  background: var(--farm-green-dark) !important;
}

/* Fix for Leaflet marker icons */
.leaflet-default-icon-path {
  background-image: url('https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png');
}

/* Custom drawing styles */
.leaflet-draw-toolbar {
  margin-top: 10px;
}

/* Ensure proper z-index for map controls */
.leaflet-control-container {
  z-index: 1000;
}

/* Zone drawing styles */
.zone-drawing-active {
  cursor: crosshair !important;
}

.zone-polygon {
  stroke: var(--farm-green) !important;
  stroke-width: 3px !important;
  fill: var(--farm-green) !important;
  fill-opacity: 0.2 !important;
}

.zone-polygon:hover {
  stroke: var(--farm-green-dark) !important;
  fill-opacity: 0.3 !important;
}

/* Responsive design improvements */
@media (max-width: 768px) {
  .floating-action-button {
    @apply bottom-4 right-4 w-14 h-14 text-xl;
  }

  .floating-panel {
    @apply m-2 p-3;
  }

  .farm-button {
    @apply px-4 py-2 text-sm;
    min-height: 40px;
  }
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading states */
.loading-spinner {
  @apply inline-block w-6 h-6 border-2 border-white/30 border-t-white rounded-full;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
