/* Mapbox GL CSS */
@import 'mapbox-gl/dist/mapbox-gl.css';

@import "tailwindcss";

/* Custom styles for AeroFarm */
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Ensure map container takes full height */
.mapboxgl-map {
  font-family: inherit;
}

/* Custom button styles for farming operations */
.operation-button {
  @apply px-4 py-2 rounded-lg font-medium transition-all duration-200;
}

.operation-button.harvest {
  @apply bg-yellow-500 hover:bg-yellow-600 text-white;
}

.operation-button.seed {
  @apply bg-green-500 hover:bg-green-600 text-white;
}

.operation-button.irrigate {
  @apply bg-blue-500 hover:bg-blue-600 text-white;
}
