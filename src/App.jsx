import React, { useState } from 'react'
import SatelliteMapSelector from './components/SatelliteMapSelector'
import TerrainViewer3D from './components/TerrainViewer3D'
import AppModeToggle from './components/AppModeToggle'

function App() {
  const [mode, setMode] = useState('2d') // '2d' or '3d'
  const [zones, setZones] = useState([])
  const [bounds, setBounds] = useState(null)

  // Handle zone selection from map
  const handleZonesSelected = (selectedZones, boundingBox) => {
    setZones(selectedZones)
    setBounds(boundingBox)
    setMode('3d') // Automatically switch to 3D view
  }

  // Handle mode changes
  const handleModeChange = (newMode) => {
    setMode(newMode)
  }

  // Handle back to map from 3D view
  const handleBackToMap = () => {
    setMode('2d')
  }

  return (
    <div className="h-screen flex flex-col relative overflow-hidden">
      {/* Floating Header - Compact and farming-themed */}
      <AppModeToggle
        mode={mode}
        onModeChange={handleModeChange}
        zones={zones}
      />

      {/* Full-screen Main content - Map takes 90%+ of space */}
      <div className="flex-1 relative">
        {mode === '2d' ? (
          <SatelliteMapSelector
            onZonesSelected={handleZonesSelected}
            selectedZones={zones}
          />
        ) : (
          <TerrainViewer3D
            zones={zones}
            bounds={bounds}
            onBackToMap={handleBackToMap}
          />
        )}
      </div>
    </div>
  )
}

export default App
