import React from 'react'
import { <PERSON><PERSON><PERSON><PERSON>, TileLayer } from 'react-leaflet'
import 'leaflet/dist/leaflet.css'

const SimpleMapTest = () => {
  return (
    <div style={{ height: '400px', width: '100%', backgroundColor: 'lightblue' }}>
      <h3>Map Test Component</h3>
      <div style={{ height: '350px', width: '100%', border: '2px solid red' }}>
        <MapContainer
          center={[40.7128, -74.0060]}
          zoom={10}
          style={{ height: '100%', width: '100%' }}
        >
          <TileLayer
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          />
        </MapContainer>
      </div>
    </div>
  )
}

export default SimpleMapTest
