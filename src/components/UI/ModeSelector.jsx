import React from 'react'

const modes = [
  {
    id: 'harvest',
    name: 'Harvest',
    icon: '🌾',
    color: 'bg-yellow-500 hover:bg-yellow-600',
    description: 'Track harvesting progress'
  },
  {
    id: 'seed',
    name: 'Seed',
    icon: '🌱',
    color: 'bg-green-500 hover:bg-green-600',
    description: 'Monitor seeding operations'
  },
  {
    id: 'irrigate',
    name: 'Irrigate',
    icon: '💧',
    color: 'bg-blue-500 hover:bg-blue-600',
    description: 'Manage irrigation zones'
  }
]

const ModeSelector = ({ currentMode, onModeChange }) => {
  return (
    <div className="space-y-3">
      <h2 className="text-lg font-semibold text-gray-800">Operation Mode</h2>
      
      <div className="grid grid-cols-1 gap-2">
        {modes.map((mode) => (
          <button
            key={mode.id}
            onClick={() => onModeChange(mode.id)}
            className={`
              p-3 rounded-lg text-white font-medium transition-all duration-200 text-left
              ${currentMode === mode.id ? mode.color : 'bg-gray-300 hover:bg-gray-400'}
            `}
          >
            <div className="flex items-center space-x-3">
              <span className="text-xl">{mode.icon}</span>
              <div>
                <div className="font-semibold">{mode.name}</div>
                <div className="text-xs opacity-90">{mode.description}</div>
              </div>
            </div>
          </button>
        ))}
      </div>
      
      {/* Current Mode Info */}
      <div className="mt-4 p-3 bg-gray-50 rounded-lg">
        <div className="text-sm text-gray-600">
          <strong>Active Mode:</strong> {modes.find(m => m.id === currentMode)?.name}
        </div>
        <div className="text-xs text-gray-500 mt-1">
          {modes.find(m => m.id === currentMode)?.description}
        </div>
      </div>
    </div>
  )
}

export default ModeSelector
