import React from 'react'

const ZonePanel = ({ zones, selectedZone, onZoneSelect, currentMode }) => {
  const getModeIcon = (mode) => {
    switch (mode) {
      case 'harvest': return '🌾'
      case 'seed': return '🌱'
      case 'irrigate': return '💧'
      default: return '📍'
    }
  }

  const getModeColor = (mode) => {
    switch (mode) {
      case 'harvest': return 'text-yellow-600 bg-yellow-50'
      case 'seed': return 'text-green-600 bg-green-50'
      case 'irrigate': return 'text-blue-600 bg-blue-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  return (
    <div className="p-4 space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-800">Farm Zones</h3>
        <button className="text-sm text-green-600 hover:text-green-700 font-medium">
          + Add Zone
        </button>
      </div>

      {zones.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <div className="text-4xl mb-2">🗺️</div>
          <p className="text-sm">No zones created yet</p>
          <p className="text-xs mt-1">Draw zones on the map to get started</p>
        </div>
      ) : (
        <div className="space-y-2">
          {zones.map((zone) => (
            <div
              key={zone.id}
              onClick={() => onZoneSelect(zone)}
              className={`
                p-3 rounded-lg border cursor-pointer transition-all duration-200
                ${selectedZone?.id === zone.id 
                  ? 'border-green-500 bg-green-50' 
                  : 'border-gray-200 hover:border-gray-300 bg-white'
                }
              `}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <span className="text-lg">{getModeIcon(zone.type || currentMode)}</span>
                  <div>
                    <div className="font-medium text-gray-800">{zone.name}</div>
                    <div className="text-xs text-gray-500">
                      {zone.area ? `${zone.area.toFixed(1)} acres` : 'Area calculating...'}
                    </div>
                  </div>
                </div>
                
                <div className={`px-2 py-1 rounded-full text-xs font-medium ${getModeColor(zone.type || currentMode)}`}>
                  {zone.type || currentMode}
                </div>
              </div>
              
              {zone.progress !== undefined && (
                <div className="mt-2">
                  <div className="flex justify-between text-xs text-gray-600 mb-1">
                    <span>Progress</span>
                    <span>{Math.round(zone.progress)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-green-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${zone.progress}%` }}
                    ></div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Zone Statistics */}
      {zones.length > 0 && (
        <div className="mt-6 p-3 bg-gray-50 rounded-lg">
          <h4 className="text-sm font-semibold text-gray-700 mb-2">Statistics</h4>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div>
              <span className="text-gray-500">Total Zones:</span>
              <span className="ml-1 font-medium">{zones.length}</span>
            </div>
            <div>
              <span className="text-gray-500">Total Area:</span>
              <span className="ml-1 font-medium">
                {zones.reduce((sum, zone) => sum + (zone.area || 0), 0).toFixed(1)} acres
              </span>
            </div>
            <div>
              <span className="text-gray-500">Completed:</span>
              <span className="ml-1 font-medium">
                {zones.filter(zone => (zone.progress || 0) >= 100).length}
              </span>
            </div>
            <div>
              <span className="text-gray-500">In Progress:</span>
              <span className="ml-1 font-medium">
                {zones.filter(zone => (zone.progress || 0) > 0 && (zone.progress || 0) < 100).length}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ZonePanel
