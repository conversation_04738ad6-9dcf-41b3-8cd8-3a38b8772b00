import React from 'react'

const AppModeToggle = ({ mode, onModeChange, zones }) => {
  return (
    <div className="floating-header absolute top-0 left-0 right-0 z-50">
      <div className="flex items-center justify-between">
        {/* Left side - Farm branding and mode toggle */}
        <div className="flex items-center space-x-6">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 rounded-full bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center">
              <span className="text-white font-bold text-sm">🌱</span>
            </div>
            <h1 className="text-xl font-bold bg-gradient-to-r from-green-600 to-green-800 bg-clip-text text-transparent">
              AeroFarm
            </h1>
          </div>

          {/* Mode Toggle Pills */}
          <div className="flex items-center bg-gray-100 rounded-full p-1">
            <button
              onClick={() => onModeChange('2d')}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                mode === '2d'
                  ? 'bg-white text-green-700 shadow-md'
                  : 'text-gray-600 hover:text-green-600'
              }`}
            >
              🗺️ Map
            </button>

            <button
              onClick={() => onModeChange('3d')}
              disabled={!zones || zones.length === 0}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                mode === '3d'
                  ? 'bg-white text-green-700 shadow-md'
                  : zones && zones.length > 0
                  ? 'text-gray-600 hover:text-green-600'
                  : 'text-gray-400 cursor-not-allowed'
              }`}
            >
              🏔️ 3D
            </button>
          </div>
        </div>

        {/* Right side - Status indicators */}
        <div className="flex items-center space-x-3">
          {/* GPS Status */}
          <div className="status-indicator gps-connected">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="hidden sm:inline">GPS</span>
          </div>

          {/* Weather */}
          <div className="status-indicator weather">
            <span>☀️</span>
            <span className="hidden sm:inline">22°C</span>
          </div>

          {/* Zone Count */}
          {zones && zones.length > 0 && (
            <div className="status-indicator zones">
              <span className="font-semibold">{zones.length}</span>
              <span className="hidden sm:inline">zone{zones.length !== 1 ? 's' : ''}</span>
            </div>
          )}

          {/* Battery indicator for mobile */}
          <div className="status-indicator bg-gray-100 text-gray-600 sm:hidden">
            <span>🔋</span>
            <span>85%</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AppModeToggle
