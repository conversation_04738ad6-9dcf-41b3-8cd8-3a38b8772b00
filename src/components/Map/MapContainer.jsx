import React, { useEffect, useRef, useState } from 'react'
import mapboxgl from 'mapbox-gl'

// Get Mapbox token from environment variables
// Create a .env file with VITE_MAPBOX_ACCESS_TOKEN=your_token_here
const MAPBOX_TOKEN = import.meta.env.VITE_MAPBOX_ACCESS_TOKEN || 'pk.demo.token'

const MapContainer = ({ zones, onZonesChange, selectedZone, currentMode }) => {
  const mapContainer = useRef(null)
  const map = useRef(null)
  const [lng, setLng] = useState(-95.7129) // Default to center of US farmland
  const [lat, setLat] = useState(37.0902)
  const [zoom, setZoom] = useState(15)
  const [isDrawing, setIsDrawing] = useState(false)

  useEffect(() => {
    if (map.current) return // Initialize map only once

    // Set the access token
    mapboxgl.accessToken = MAPBOX_TOKEN

    map.current = new mapboxgl.Map({
      container: mapContainer.current,
      style: 'mapbox://styles/mapbox/satellite-v9', // Satellite view for farming
      center: [lng, lat],
      zoom: zoom,
      pitch: 45, // 3D tilt
      bearing: 0
    })

    // Add navigation controls
    map.current.addControl(new mapboxgl.NavigationControl(), 'top-right')

    // Add geolocate control
    map.current.addControl(
      new mapboxgl.GeolocateControl({
        positionOptions: {
          enableHighAccuracy: true
        },
        trackUserLocation: true,
        showUserHeading: true
      }),
      'top-right'
    )

    // Update state on map move
    map.current.on('move', () => {
      setLng(map.current.getCenter().lng.toFixed(4))
      setLat(map.current.getCenter().lat.toFixed(4))
      setZoom(map.current.getZoom().toFixed(2))
    })

    // Add terrain and 3D buildings
    map.current.on('style.load', () => {
      map.current.addSource('mapbox-dem', {
        type: 'raster-dem',
        url: 'mapbox://mapbox.mapbox-terrain-dem-v1',
        tileSize: 512,
        maxzoom: 14
      })
      
      map.current.setTerrain({ source: 'mapbox-dem', exaggeration: 1.5 })
      
      // Add sky layer for better 3D effect
      map.current.addLayer({
        id: 'sky',
        type: 'sky',
        paint: {
          'sky-type': 'atmosphere',
          'sky-atmosphere-sun': [0.0, 0.0],
          'sky-atmosphere-sun-intensity': 15
        }
      })
    })

    return () => map.current?.remove()
  }, [])

  // Mock GPS position (replace with real GPS in production)
  useEffect(() => {
    const mockGPSPosition = () => {
      const mockLng = lng + (Math.random() - 0.5) * 0.001
      const mockLat = lat + (Math.random() - 0.5) * 0.001
      
      // Add or update GPS marker
      const gpsMarker = new mapboxgl.Marker({
        color: '#ff0000',
        scale: 0.8
      })
        .setLngLat([mockLng, mockLat])
        .addTo(map.current)
      
      return () => gpsMarker.remove()
    }

    const interval = setInterval(mockGPSPosition, 5000) // Update every 5 seconds
    return () => clearInterval(interval)
  }, [lng, lat])

  const handleMapClick = (e) => {
    if (!isDrawing) return

    // Simple zone creation - in a real app, you'd use Mapbox Draw
    const newZone = {
      id: Date.now().toString(),
      name: `Zone ${zones.length + 1}`,
      center: [e.lngLat.lng, e.lngLat.lat],
      type: currentMode,
      area: Math.random() * 10 + 5, // Mock area calculation
      progress: 0,
      coordinates: [
        // Simple square around click point
        [e.lngLat.lng - 0.001, e.lngLat.lat - 0.001],
        [e.lngLat.lng + 0.001, e.lngLat.lat - 0.001],
        [e.lngLat.lng + 0.001, e.lngLat.lat + 0.001],
        [e.lngLat.lng - 0.001, e.lngLat.lat + 0.001],
        [e.lngLat.lng - 0.001, e.lngLat.lat - 0.001]
      ]
    }

    onZonesChange([...zones, newZone])
    setIsDrawing(false)
  }

  useEffect(() => {
    if (!map.current) return

    const handleClick = (e) => handleMapClick(e)
    
    if (isDrawing) {
      map.current.on('click', handleClick)
      map.current.getCanvas().style.cursor = 'crosshair'
    } else {
      map.current.off('click', handleClick)
      map.current.getCanvas().style.cursor = ''
    }

    return () => {
      map.current?.off('click', handleClick)
    }
  }, [isDrawing, zones, currentMode])

  return (
    <div className="relative h-full">
      {/* Map Container */}
      <div ref={mapContainer} className="h-full" />
      
      {/* Map Controls */}
      <div className="absolute top-4 left-4 bg-white rounded-lg shadow-lg p-3 space-y-2">
        <button
          onClick={() => setIsDrawing(!isDrawing)}
          className={`
            px-3 py-2 rounded text-sm font-medium transition-colors
            ${isDrawing 
              ? 'bg-red-500 hover:bg-red-600 text-white' 
              : 'bg-green-500 hover:bg-green-600 text-white'
            }
          `}
        >
          {isDrawing ? 'Cancel Drawing' : 'Draw Zone'}
        </button>
        
        <div className="text-xs text-gray-600">
          <div>Lng: {lng}</div>
          <div>Lat: {lat}</div>
          <div>Zoom: {zoom}</div>
        </div>
      </div>

      {/* Mode Indicator */}
      <div className="absolute bottom-4 left-4 bg-white rounded-lg shadow-lg p-3">
        <div className="text-sm font-medium text-gray-700">
          Current Mode: <span className="capitalize text-green-600">{currentMode}</span>
        </div>
        {isDrawing && (
          <div className="text-xs text-gray-500 mt-1">
            Click on the map to create a new zone
          </div>
        )}
      </div>

      {/* Zone Count */}
      {zones.length > 0 && (
        <div className="absolute bottom-4 right-4 bg-white rounded-lg shadow-lg p-3">
          <div className="text-sm font-medium text-gray-700">
            Zones: {zones.length}
          </div>
          <div className="text-xs text-gray-500">
            Total Area: {zones.reduce((sum, zone) => sum + (zone.area || 0), 0).toFixed(1)} acres
          </div>
        </div>
      )}
    </div>
  )
}

export default MapContainer
