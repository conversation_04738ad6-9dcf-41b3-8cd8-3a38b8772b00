import React, { Suspense, useRef, useEffect, useState } from 'react'
import { Canvas, useFrame, useThree } from '@react-three/fiber'
import { OrbitControls, Text } from '@react-three/drei'
import * as THREE from 'three'

// Terrain mesh component
const TerrainMesh = ({ elevationData, satelliteTexture, bounds }) => {
  const meshRef = useRef()
  const [geometry, setGeometry] = useState(null)

  useEffect(() => {
    if (!elevationData || !elevationData.length) return

    // Create terrain geometry from elevation data
    const width = elevationData[0].length
    const height = elevationData.length
    const widthSegments = width - 1
    const heightSegments = height - 1

    // Create plane geometry
    const geo = new THREE.PlaneGeometry(
      10, // width in 3D units
      10, // height in 3D units
      widthSegments,
      heightSegments
    )

    // Apply elevation data to vertices
    const vertices = geo.attributes.position.array
    for (let i = 0; i < height; i++) {
      for (let j = 0; j < width; j++) {
        const index = (i * width + j) * 3 + 2 // z coordinate
        const elevation = elevationData[i][j] || 0
        vertices[index] = elevation * 0.01 // Scale elevation
      }
    }

    geo.attributes.position.needsUpdate = true
    geo.computeVertexNormals()

    setGeometry(geo)
  }, [elevationData])

  useFrame((state) => {
    if (meshRef.current) {
      // Optional: Add subtle animation or updates
    }
  })

  if (!geometry) return null

  return (
    <mesh ref={meshRef} geometry={geometry} rotation={[-Math.PI / 2, 0, 0]}>
      <meshStandardMaterial
        color="#4ade80"
        wireframe={false}
        side={THREE.DoubleSide}
      />
    </mesh>
  )
}

// Loading component
const LoadingIndicator = () => (
  <Text
    position={[0, 0, 0]}
    fontSize={0.5}
    color="white"
    anchorX="center"
    anchorY="middle"
  >
    Loading 3D Terrain...
  </Text>
)

// Camera controller
const CameraController = ({ bounds }) => {
  const { camera, gl } = useThree()

  useEffect(() => {
    if (bounds) {
      // Position camera to view the terrain
      camera.position.set(5, 5, 5)
      camera.lookAt(0, 0, 0)
    }
  }, [camera, bounds])

  return null
}

// Zone overlay component
const ZoneOverlay = ({ zones }) => {
  if (!zones || zones.length === 0) return null

  return (
    <group>
      {zones.map((zone, index) => (
        <Text
          key={zone.id}
          position={[index * 2 - zones.length, 2, 0]}
          fontSize={0.3}
          color="#22c55e"
          anchorX="center"
          anchorY="middle"
        >
          {zone.name}
        </Text>
      ))}
    </group>
  )
}

const TerrainViewer3D = ({ zones, bounds, onBackToMap }) => {
  const [elevationData, setElevationData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  // Generate mock elevation data for now
  useEffect(() => {
    const generateMockElevation = () => {
      const size = 50
      const data = []
      
      for (let i = 0; i < size; i++) {
        const row = []
        for (let j = 0; j < size; j++) {
          // Generate some interesting terrain with hills and valleys
          const x = (i - size / 2) / 10
          const y = (j - size / 2) / 10
          const elevation = Math.sin(x) * Math.cos(y) * 50 + 
                          Math.sin(x * 2) * Math.cos(y * 2) * 25 +
                          Math.random() * 10
          row.push(elevation)
        }
        data.push(row)
      }
      
      return data
    }

    // Simulate loading delay
    setTimeout(() => {
      try {
        const mockData = generateMockElevation()
        setElevationData(mockData)
        setLoading(false)
      } catch (err) {
        setError('Failed to generate terrain data')
        setLoading(false)
      }
    }, 1000)
  }, [bounds])

  if (error) {
    return (
      <div className="h-full flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <div className="text-red-500 text-xl mb-2">⚠️</div>
          <h3 className="text-lg font-medium text-gray-800 mb-2">Error Loading Terrain</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={onBackToMap}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Back to Map
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full relative">
      {/* Full-screen 3D Canvas */}
      <div className="absolute inset-0 pt-20 bg-gradient-to-b from-blue-100 to-green-100">
        <Canvas
          camera={{ position: [5, 5, 5], fov: 60 }}
          style={{ width: '100%', height: '100%' }}
        >
          {/* Enhanced lighting for farming visualization */}
          <ambientLight intensity={0.6} />
          <directionalLight
            position={[10, 10, 5]}
            intensity={1.2}
            castShadow
            shadow-mapSize-width={2048}
            shadow-mapSize-height={2048}
            color="#FFF8DC" // Warm sunlight
          />
          <pointLight position={[-10, 5, -10]} intensity={0.3} color="#87CEEB" />

          {/* Controls */}
          <OrbitControls
            enablePan={true}
            enableZoom={true}
            enableRotate={true}
            minDistance={2}
            maxDistance={20}
            maxPolarAngle={Math.PI / 2}
          />

          {/* Camera controller */}
          <CameraController bounds={bounds} />

          {/* Content */}
          <Suspense fallback={<LoadingIndicator />}>
            {loading ? (
              <LoadingIndicator />
            ) : (
              <group>
                {/* Terrain with farming colors */}
                <TerrainMesh
                  elevationData={elevationData}
                  bounds={bounds}
                />

                {/* Zone overlays */}
                <ZoneOverlay zones={zones} />

                {/* Enhanced ground plane */}
                <mesh position={[0, -0.1, 0]} rotation={[-Math.PI / 2, 0, 0]}>
                  <planeGeometry args={[20, 20]} />
                  <meshBasicMaterial color="#8B4513" opacity={0.4} transparent />
                </mesh>
              </group>
            )}
          </Suspense>
        </Canvas>
      </div>

      {/* Floating Back Button */}
      <button
        onClick={onBackToMap}
        className="floating-panel absolute top-24 left-4 px-4 py-2 flex items-center space-x-2 hover:shadow-xl transition-all duration-300"
      >
        <span>←</span>
        <span className="font-medium">Back to Map</span>
      </button>

      {/* Floating 3D Info Panel */}
      <div className="floating-panel absolute top-24 right-4 w-64 fade-in">
        <div className="flex items-center space-x-2 mb-3">
          <span className="text-2xl">🏔️</span>
          <h3 className="font-semibold text-gray-800">3D Terrain View</h3>
        </div>

        <div className="space-y-2 text-sm text-gray-600">
          <div className="flex justify-between">
            <span>Zones:</span>
            <span className="font-medium">{zones?.length || 0}</span>
          </div>
          <div className="flex justify-between">
            <span>Status:</span>
            <span className="font-medium text-green-600">
              {loading ? 'Loading...' : 'Ready'}
            </span>
          </div>
        </div>

        <div className="mt-4 p-3 bg-green-50 rounded-lg">
          <h4 className="font-medium text-green-800 mb-2">Controls</h4>
          <div className="text-xs text-green-700 space-y-1">
            <div>🖱️ Drag to rotate</div>
            <div>🔍 Scroll to zoom</div>
            <div>⌨️ Right-click + drag to pan</div>
          </div>
        </div>
      </div>

      {/* Loading Overlay */}
      {loading && (
        <div className="absolute inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="floating-panel text-center">
            <div className="loading-spinner mx-auto mb-3"></div>
            <h3 className="font-semibold text-gray-800 mb-2">Loading 3D Terrain</h3>
            <p className="text-sm text-gray-600">Analyzing elevation data...</p>
          </div>
        </div>
      )}
    </div>
  )
}

export default TerrainViewer3D
