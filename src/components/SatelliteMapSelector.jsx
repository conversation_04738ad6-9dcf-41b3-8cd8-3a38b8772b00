import React, { useState } from 'react'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Polygon, useMapEvents } from 'react-leaflet'
import 'leaflet/dist/leaflet.css'

// Fix for default markers in react-leaflet
import L from 'leaflet'

// Fix Leaflet default icon issue
delete L.Icon.Default.prototype._getIconUrl
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon-2x.png',
  iconUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png',
  shadowUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png',
})

// Custom drawing component
const DrawingHandler = ({ onZoneCreated, isDrawing, setIsDrawing }) => {
  const [currentPoints, setCurrentPoints] = useState([])

  useMapEvents({
    click(e) {
      if (isDrawing) {
        const newPoint = [e.latlng.lat, e.latlng.lng]
        setCurrentPoints(prev => [...prev, newPoint])
      }
    },
    keydown(e) {
      if (e.originalEvent.key === 'Enter' && currentPoints.length >= 3) {
        onZoneCreated(currentPoints)
        setCurrentPoints([])
        setIsDrawing(false)
      }
      if (e.originalEvent.key === 'Escape') {
        setCurrentPoints([])
        setIsDrawing(false)
      }
    }
  })

  return currentPoints.length > 0 ? (
    <Polygon
      positions={currentPoints}
      pathOptions={{ color: '#22c55e', weight: 2, fillOpacity: 0.2 }}
    />
  ) : null
}

const SatelliteMapSelector = ({ onZonesSelected, selectedZones = [] }) => {
  const [zones, setZones] = useState(selectedZones)
  const [mapCenter] = useState([40.7128, -74.0060]) // Default to NYC, will be replaced with user location
  const [isDrawing, setIsDrawing] = useState(false)

  // Handle zone creation
  const handleZoneCreated = (coordinates) => {
    const newZone = {
      id: Date.now(),
      name: `Zone ${zones.length + 1}`,
      coordinates,
      type: 'general',
      area: calculateArea(coordinates)
    }

    const updatedZones = [...zones, newZone]
    setZones(updatedZones)
  }

  // Start drawing mode
  const startDrawing = () => {
    setIsDrawing(true)
  }

  // Delete zone
  const deleteZone = (zoneId) => {
    const updatedZones = zones.filter(zone => zone.id !== zoneId)
    setZones(updatedZones)
  }

  // Calculate approximate area of polygon (simplified)
  const calculateArea = (coordinates) => {
    if (coordinates.length < 3) return 0
    
    let area = 0
    const n = coordinates.length
    
    for (let i = 0; i < n; i++) {
      const j = (i + 1) % n
      area += coordinates[i][0] * coordinates[j][1]
      area -= coordinates[j][0] * coordinates[i][1]
    }
    
    return Math.abs(area) / 2 * 111000 * 111000 // Rough conversion to square meters
  }

  // Calculate bounding box for all zones
  const getBoundingBox = () => {
    if (zones.length === 0) return null
    
    let minLat = Infinity, maxLat = -Infinity
    let minLng = Infinity, maxLng = -Infinity
    
    zones.forEach(zone => {
      zone.coordinates.forEach(([lat, lng]) => {
        minLat = Math.min(minLat, lat)
        maxLat = Math.max(maxLat, lat)
        minLng = Math.min(minLng, lng)
        maxLng = Math.max(maxLng, lng)
      })
    })
    
    return {
      north: maxLat,
      south: minLat,
      east: maxLng,
      west: minLng,
      center: [(minLat + maxLat) / 2, (minLng + maxLng) / 2]
    }
  }

  return (
    <div className="h-full relative">
      {/* Full-screen Map */}
      <div className="absolute inset-0 pt-20"> {/* Add top padding for floating header */}
        <MapContainer
          center={mapCenter}
          zoom={13}
          style={{ height: '100%', width: '100%' }}
          className={isDrawing ? 'zone-drawing-active' : ''}
        >
          {/* Satellite imagery layer */}
          <TileLayer
            url="https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
            attribution='&copy; <a href="https://www.esri.com/">Esri</a>'
          />

          {/* Drawing handler */}
          <DrawingHandler
            onZoneCreated={handleZoneCreated}
            isDrawing={isDrawing}
            setIsDrawing={setIsDrawing}
          />

          {/* Existing zones with farming colors */}
          {zones.map((zone, index) => (
            <Polygon
              key={zone.id}
              positions={zone.coordinates}
              pathOptions={{
                color: '#2D5016',
                weight: 3,
                fillOpacity: 0.25,
                fillColor: index % 2 === 0 ? '#22C55E' : '#F4A261',
                className: 'zone-polygon'
              }}
            />
          ))}
        </MapContainer>
      </div>

      {/* Floating Action Button for Drawing */}
      {!isDrawing && (
        <button
          onClick={startDrawing}
          className="floating-action-button fade-in"
          title="Draw new zone"
        >
          ✏️
        </button>
      )}

      {/* Floating Instructions Panel */}
      {isDrawing && (
        <div className="floating-panel absolute top-24 left-4 right-4 sm:left-6 sm:right-auto sm:w-80 slide-up">
          <div className="flex items-center space-x-3 mb-3">
            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
            <h3 className="font-semibold text-gray-800">Drawing Zone</h3>
          </div>
          <p className="text-sm text-gray-600 mb-4">
            Tap on the map to add points. Press <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Enter</kbd> to finish or <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Escape</kbd> to cancel.
          </p>
          <div className="flex space-x-2">
            <button
              onClick={() => setIsDrawing(false)}
              className="farm-button bg-gray-500 hover:bg-gray-600 text-white text-sm flex-1"
            >
              Cancel
            </button>
          </div>
        </div>
      )}

      {/* Floating Zone Summary Panel */}
      {zones.length > 0 && !isDrawing && (
        <div className="floating-panel absolute bottom-6 left-4 right-4 sm:left-6 sm:right-auto sm:w-80 fade-in">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold text-gray-800">Farm Zones</h3>
            <span className="status-indicator zones">
              {zones.length} zone{zones.length !== 1 ? 's' : ''}
            </span>
          </div>

          <div className="space-y-2 mb-4">
            {zones.map((zone, index) => (
              <div key={zone.id} className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: index % 2 === 0 ? '#22C55E' : '#F4A261' }}
                  ></div>
                  <span className="text-sm font-medium">Zone {index + 1}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-xs text-gray-600">{(zone.area / 10000).toFixed(1)} ha</span>
                  <button
                    onClick={() => deleteZone(zone.id)}
                    className="text-red-500 hover:text-red-700 text-xs w-5 h-5 flex items-center justify-center rounded-full hover:bg-red-50"
                  >
                    ✕
                  </button>
                </div>
              </div>
            ))}
          </div>

          <button
            onClick={() => onZonesSelected(zones, getBoundingBox())}
            className="farm-button farm-button-primary w-full"
          >
            🏔️ Generate 3D View
          </button>
        </div>
      )}

      {/* Welcome Message for Empty State */}
      {zones.length === 0 && !isDrawing && (
        <div className="floating-panel absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center fade-in">
          <div className="text-6xl mb-4">🚜</div>
          <h2 className="text-xl font-bold text-gray-800 mb-2">Welcome to AeroFarm</h2>
          <p className="text-gray-600 mb-4">Start by drawing your first farm zone</p>
          <button
            onClick={startDrawing}
            className="farm-button farm-button-primary"
          >
            ✏️ Draw Your First Zone
          </button>
        </div>
      )}
    </div>
  )
}

export default SatelliteMapSelector
