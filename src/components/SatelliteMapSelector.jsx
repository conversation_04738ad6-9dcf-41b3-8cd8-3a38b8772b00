import React, { useState } from 'react'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Polygon, useMapEvents } from 'react-leaflet'
import 'leaflet/dist/leaflet.css'

// Fix for default markers in react-leaflet
import L from 'leaflet'

// Fix Leaflet default icon issue
delete L.Icon.Default.prototype._getIconUrl
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon-2x.png',
  iconUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png',
  shadowUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png',
})

// Custom drawing component
const DrawingHandler = ({ onZoneCreated, isDrawing, setIsDrawing }) => {
  const [currentPoints, setCurrentPoints] = useState([])

  useMapEvents({
    click(e) {
      if (isDrawing) {
        const newPoint = [e.latlng.lat, e.latlng.lng]
        setCurrentPoints(prev => [...prev, newPoint])
      }
    },
    keydown(e) {
      if (e.originalEvent.key === 'Enter' && currentPoints.length >= 3) {
        onZoneCreated(currentPoints)
        setCurrentPoints([])
        setIsDrawing(false)
      }
      if (e.originalEvent.key === 'Escape') {
        setCurrentPoints([])
        setIsDrawing(false)
      }
    }
  })

  return currentPoints.length > 0 ? (
    <Polygon
      positions={currentPoints}
      pathOptions={{ color: '#22c55e', weight: 2, fillOpacity: 0.2 }}
    />
  ) : null
}

const SatelliteMapSelector = ({ onZonesSelected, selectedZones = [] }) => {
  const [zones, setZones] = useState(selectedZones)
  const [mapCenter] = useState([40.7128, -74.0060]) // Default to NYC, will be replaced with user location
  const [isDrawing, setIsDrawing] = useState(false)

  // Handle zone creation
  const handleZoneCreated = (coordinates) => {
    const newZone = {
      id: Date.now(),
      name: `Zone ${zones.length + 1}`,
      coordinates,
      type: 'general',
      area: calculateArea(coordinates)
    }

    const updatedZones = [...zones, newZone]
    setZones(updatedZones)
  }

  // Start drawing mode
  const startDrawing = () => {
    setIsDrawing(true)
  }

  // Delete zone
  const deleteZone = (zoneId) => {
    const updatedZones = zones.filter(zone => zone.id !== zoneId)
    setZones(updatedZones)
  }

  // Calculate approximate area of polygon (simplified)
  const calculateArea = (coordinates) => {
    if (coordinates.length < 3) return 0
    
    let area = 0
    const n = coordinates.length
    
    for (let i = 0; i < n; i++) {
      const j = (i + 1) % n
      area += coordinates[i][0] * coordinates[j][1]
      area -= coordinates[j][0] * coordinates[i][1]
    }
    
    return Math.abs(area) / 2 * 111000 * 111000 // Rough conversion to square meters
  }

  // Calculate bounding box for all zones
  const getBoundingBox = () => {
    if (zones.length === 0) return null
    
    let minLat = Infinity, maxLat = -Infinity
    let minLng = Infinity, maxLng = -Infinity
    
    zones.forEach(zone => {
      zone.coordinates.forEach(([lat, lng]) => {
        minLat = Math.min(minLat, lat)
        maxLat = Math.max(maxLat, lat)
        minLng = Math.min(minLng, lng)
        maxLng = Math.max(maxLng, lng)
      })
    })
    
    return {
      north: maxLat,
      south: minLat,
      east: maxLng,
      west: minLng,
      center: [(minLat + maxLat) / 2, (minLng + maxLng) / 2]
    }
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="bg-white p-4 shadow-md">
        <h2 className="text-xl font-bold text-gray-800">Select Farm Zones</h2>
        <p className="text-sm text-gray-600">
          {isDrawing ? 'Click to add points, press Enter to finish, Escape to cancel' : 'Click "Draw Zone" to start drawing'}
        </p>

        <div className="mt-2 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <button
              onClick={startDrawing}
              disabled={isDrawing}
              className={`px-3 py-1 rounded text-sm ${
                isDrawing
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-blue-600 text-white hover:bg-blue-700'
              }`}
            >
              {isDrawing ? 'Drawing...' : 'Draw Zone'}
            </button>

            {zones.length > 0 && (
              <span className="text-sm text-green-600">
                {zones.length} zone{zones.length !== 1 ? 's' : ''} selected
              </span>
            )}
          </div>

          {zones.length > 0 && (
            <button
              onClick={() => onZonesSelected(zones, getBoundingBox())}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              Generate 3D View
            </button>
          )}
        </div>
      </div>

      {/* Map */}
      <div className="flex-1 bg-gray-200" style={{ minHeight: '400px' }}>
        <MapContainer
          center={mapCenter}
          zoom={13}
          style={{ height: '100%', width: '100%', minHeight: '400px' }}
        >
          {/* Satellite imagery layer */}
          <TileLayer
            url="https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
            attribution='&copy; <a href="https://www.esri.com/">Esri</a>'
          />

          {/* Drawing handler */}
          <DrawingHandler
            onZoneCreated={handleZoneCreated}
            isDrawing={isDrawing}
            setIsDrawing={setIsDrawing}
          />

          {/* Existing zones */}
          {zones.map((zone) => (
            <Polygon
              key={zone.id}
              positions={zone.coordinates}
              pathOptions={{
                color: '#22c55e',
                weight: 2,
                fillOpacity: 0.3,
                fillColor: '#22c55e'
              }}
            />
          ))}
        </MapContainer>
      </div>

      {/* Zone list */}
      {zones.length > 0 && (
        <div className="bg-white p-4 border-t max-h-32 overflow-y-auto">
          <h3 className="font-medium text-gray-800 mb-2">Selected Zones:</h3>
          <div className="space-y-1">
            {zones.map((zone) => (
              <div key={zone.id} className="flex justify-between items-center text-sm">
                <span className="text-gray-700">{zone.name}</span>
                <div className="flex items-center space-x-2">
                  <span className="text-gray-500">
                    {(zone.area / 10000).toFixed(2)} hectares
                  </span>
                  <button
                    onClick={() => deleteZone(zone.id)}
                    className="text-red-500 hover:text-red-700 text-xs"
                  >
                    ✕
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default SatelliteMapSelector
