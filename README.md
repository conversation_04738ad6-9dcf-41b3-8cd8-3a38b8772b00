# AeroFarm - Precision Agriculture Platform

A 3D farming interface for tablets and smartphones with GPS tracking for harvest and seeding zones, satellite imagery integration for terrain visualization, and focus on irrigation and water management features.

## Features

- **3D Satellite Zone Visualization**: Interactive 3D satellite view of farming zones
- **Real-time Operation Tracking**: GPS-based tracking showing completed vs. remaining work areas
- **Multi-Mode Operations**: Different modes (Harvesting, Seeding, Irrigation)
- **Water & Irrigation Management**: Irrigation zone mapping and water distribution tracking
- **Progress Analytics**: Historical data and progress reports

## Tech Stack

- **Frontend**: React + Vite + Tailwind CSS
- **3D Mapping**: Mapbox GL JS with satellite imagery and 3D terrain
- **Mobile**: Progressive Web App (PWA) compatible

## Setup

1. **Clone and install dependencies:**
   ```bash
   npm install
   ```

2. **Set up Mapbox token:**
   - Get a free token from [Mapbox](https://account.mapbox.com/access-tokens/)
   - Copy `.env.example` to `.env`
   - Add your token: `VITE_MAPBOX_ACCESS_TOKEN=your_token_here`

3. **Start development server:**
   ```bash
   npm run dev
   ```

4. **Open in browser:**
   - Visit `http://localhost:5173`
   - Best viewed on tablet/mobile for intended experience

## Usage

1. **Select Operation Mode**: Choose between Harvest, Seed, or Irrigate
2. **Draw Zones**: Click "Draw Zone" and click on the map to create farming zones
3. **Track Progress**: Monitor completion status for each zone
4. **GPS Simulation**: Red marker shows simulated GPS position

## Project Structure

```
src/
├── components/
│   ├── Map/MapContainer.jsx      # 3D Mapbox integration
│   ├── UI/ModeSelector.jsx       # Operation mode switching
│   ├── UI/ZonePanel.jsx          # Zone management panel
│   └── Layout/Header.jsx         # App header with status
├── hooks/                        # Custom React hooks (future)
├── utils/                        # Utility functions (future)
└── App.jsx                       # Main application component
```

## Development Roadmap

See `PROTOTYPE_PLAN.md` for detailed development phases and `DESIGN.md` for project vision.

### Phase 1 (Current): Core Visualization ✅
- Basic 3D map interface
- Zone management
- Operation modes

### Phase 2 (Next): Tracking & Progress
- Enhanced GPS tracking
- Progress visualization
- Data persistence

### Phase 3 (Future): Mobile & Polish
- Mobile optimization
- Offline functionality
- User experience improvements
