{"name": "tinyqueue", "version": "3.0.0", "description": "The smallest and simplest JavaScript priority queue", "type": "module", "main": "index.js", "module": "index.js", "types": "index.d.ts", "scripts": {"lint": "eslint *.js", "pretest": "npm run lint", "test": "node --test", "bench": "node bench.js", "prepublishOnly": "npm test"}, "files": ["index.d.ts"], "repository": {"type": "git", "url": "https://github.com/mourner/tinyqueue.git"}, "keywords": ["queue", "priority", "binary heap", "data structures"], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/mourner/tinyqueue/issues"}, "homepage": "https://github.com/mourner/tinyqueue", "devDependencies": {"eslint": "^9.6.0", "eslint-config-mourner": "^4.0.1"}}